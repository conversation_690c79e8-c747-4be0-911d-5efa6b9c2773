# PowerShell script to set up Apache for Docker deployment
# Run this script as Administrator

# Configuration variables
$apacheDownloadUrl = "https://www.apachelounge.com/download/VS17/binaries/httpd-2.4.63-250207-win64-VS17.zip"
$vcRedistDownloadUrl = "https://aka.ms/vs/17/release/VC_redist.x64.exe"
$apacheZipPath = "$env:TEMP\apache.zip"
$apacheExtractPath = "C:\Apache24"
$apacheConfigPath = "C:\Apache24\conf\httpd.conf"
$apacheVhostsPath = "C:\Apache24\conf\extra\httpd-vhosts.conf"

# Function to check if running as administrator
function Test-Administrator {
    $user = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal $user
    $principal.IsInRole([Security.Principal.WindowsBuiltinRole]::Administrator)
}

# Check if running as administrator
if (-not (Test-Administrator)) {
    Write-Host "This script must be run as Administrator. Please restart PowerShell as Administrator." -ForegroundColor Red
    exit
}

# Create directory for Apache if it doesn't exist
if (-not (Test-Path -Path $apacheExtractPath)) {
    New-Item -ItemType Directory -Path $apacheExtractPath | Out-Null
}

# Download and install Visual C++ Redistributable
Write-Host "Downloading Visual C++ Redistributable..." -ForegroundColor Cyan
$vcRedistPath = "$env:TEMP\vc_redist.x64.exe"
Invoke-WebRequest -Uri $vcRedistDownloadUrl -OutFile $vcRedistPath
Write-Host "Installing Visual C++ Redistributable..." -ForegroundColor Cyan
Start-Process -FilePath $vcRedistPath -ArgumentList "/quiet", "/norestart" -Wait
Write-Host "Visual C++ Redistributable installed." -ForegroundColor Green

# Download Apache
Write-Host "Downloading Apache HTTP Server..." -ForegroundColor Cyan
Invoke-WebRequest -Uri $apacheDownloadUrl -OutFile $apacheZipPath
Write-Host "Apache HTTP Server downloaded." -ForegroundColor Green

# Extract Apache
Write-Host "Extracting Apache HTTP Server..." -ForegroundColor Cyan
Expand-Archive -Path $apacheZipPath -DestinationPath "C:\" -Force
Write-Host "Apache HTTP Server extracted to $apacheExtractPath." -ForegroundColor Green

# Configure Apache
Write-Host "Configuring Apache..." -ForegroundColor Cyan

# Enable required modules in httpd.conf
$httpdConf = Get-Content -Path $apacheConfigPath
$httpdConf = $httpdConf -replace '#LoadModule proxy_module modules/mod_proxy.so', 'LoadModule proxy_module modules/mod_proxy.so'
$httpdConf = $httpdConf -replace '#LoadModule proxy_http_module modules/mod_proxy_http.so', 'LoadModule proxy_http_module modules/mod_proxy_http.so'
$httpdConf = $httpdConf -replace '#LoadModule proxy_wstunnel_module modules/mod_proxy_wstunnel.so', 'LoadModule proxy_wstunnel_module modules/mod_proxy_wstunnel.so'
$httpdConf = $httpdConf -replace '#Include conf/extra/httpd-vhosts.conf', 'Include conf/extra/httpd-vhosts.conf'
Set-Content -Path $apacheConfigPath -Value $httpdConf

# Copy virtual host configuration
Copy-Item -Path ".\apache-config\httpd-vhosts.conf" -Destination $apacheVhostsPath -Force

# Install and start Apache service
Write-Host "Installing Apache as a Windows service..." -ForegroundColor Cyan
Set-Location -Path "C:\Apache24\bin"
Start-Process -FilePath "httpd.exe" -ArgumentList "-k", "install" -Wait
Write-Host "Starting Apache service..." -ForegroundColor Cyan
Start-Process -FilePath "httpd.exe" -ArgumentList "-k", "start" -Wait
Write-Host "Apache service started." -ForegroundColor Green

# Start Docker containers
Write-Host "Starting Docker containers..." -ForegroundColor Cyan
docker compose down
docker compose up -d
Write-Host "Docker containers started." -ForegroundColor Green

# Verify Docker containers are running
Write-Host "Verifying Docker containers..." -ForegroundColor Cyan
docker ps

Write-Host "`nSetup complete!" -ForegroundColor Green
Write-Host "You can now access your application at http://localhost" -ForegroundColor Green
Write-Host "For more information, refer to the apache-docker-deployment-guide.md file." -ForegroundColor Green
