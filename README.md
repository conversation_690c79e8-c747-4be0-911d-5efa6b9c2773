# fullstack_practice





1. Restart your Docker containers

# Stop all running containers
docker compose down

# Start containers in the background
docker compose up -d


2. Check if containers are running

docker ps

You should see your containers listed (db, backend, frontend).

3. Check backend logs for connection status

# View logs from the backend container
docker logs pern_backend

Look for the connection message we added to db.js:

Success: "Successfully connected to PostgreSQL at: [timestamp]"
Failure: "Error connecting to PostgreSQL: [error message]"


4. Check database container directly
# Connect to PostgreSQL inside the container
docker exec -it pern_db psql -U postgres -d pern_todo

# Once connected, you can run:
\dt  # List tables
SELECT NOW();  # Test query
\q   # Quit

5. Check network connectivity between containers

# Connect to the backend container
docker exec -it pern_backend sh

# From inside the backend container, test connection to db
ping db
# Or use a more direct test
nc -zv db 5432



If you're still having issues, check the database logs:

docker logs pern_db


Manual Deployment Guide for Docker Containers with Apache on Windows


Step 1: Install Apache HTTP Server
Download Apache HTTP Server for Windows (64-bit version):
Go to: https://www.apachelounge.com/download/
Download the latest Apache HTTP Server (httpd-2.4.63-250207-win64-VS17.zip)
Install the Visual C++ Redistributable (required for Apache):
Go to: https://aka.ms/vs/17/release/VC_redist.x64.exe
Download and install the Visual C++ Redistributable
Extract Apache to C:\Apache24:
Extract the downloaded Apache zip file to C:\Apache24
Install Apache as a Windows service:
Open Command Prompt as Administrator
Run the following commands:

cd C:\Apache24\bin
httpd.exe -k install

5, Start the Apache service

httpd.exe -k start


6 Verify Apache is running:
Open a browser and navigate to http://localhost
You should see the Apache default page



Step 2: Configure Apache as a Reverse Proxy
Edit the Apache configuration file:
Open C:\Apache24\conf\httpd.conf in a text editor (as Administrator)
Uncomment (remove the # at the beginning) the following lines:



LoadModule proxy_module modules/mod_proxy.so
LoadModule proxy_http_module modules/mod_proxy_http.so
LoadModule proxy_wstunnel_module modules/mod_proxy_wstunnel.so


Also uncomment:
Include conf/extra/httpd-vhosts.conf


Create a virtual host configuration:
Open C:\Apache24\conf\extra\httpd-vhosts.conf in a text editor (as Administrator)
Replace its contents with:

<VirtualHost *:80>
    ServerName localhost
    ServerAdmin webmaster@localhost
    DocumentRoot "C:/Apache24/htdocs"
    ErrorLog "logs/localhost-error.log"
    CustomLog "logs/localhost-access.log" common

    # Proxy for frontend (React app)
    ProxyPass / http://localhost:3000/
    ProxyPassReverse / http://localhost:3000/

    # Proxy for backend API
    ProxyPass /api http://localhost:5000/
    ProxyPassReverse /api http://localhost:5000/
</VirtualHost>



Restart Apache to apply the changes:

httpd.exe -k restart



Step 3: Start Your Docker Containers
Ensure Docker Desktop is running:
Start Docker Desktop from the Start menu or taskbar
Start your Docker containers:
Open Command Prompt
Navigate to your project directory
Run:


docker compose up -d

Verify that your containers are running:

docker ps

You should see your containers listed (db, backend, frontend).
Step 4: Test the Deployment
Open a browser and navigate to:

http://localhost

You should see your React frontend application.
Test the backend API by navigating to:

http://localhost/api/test-get

You should receive a JSON response from your backend.


Troubleshooting
Apache won't start:
Check the error logs at C:\Apache24\logs\error.log
Ensure all required modules are properly loaded
Verify the syntax of your configuration files with httpd.exe -t
Cannot connect to Docker containers:
Ensure Docker containers are running with docker ps
Check container logs with docker logs [container_name]
Verify network connectivity between Apache and Docker
Changes not reflecting:
Restart Apache with httpd.exe -k restart
Restart Docker containers with docker compose down followed by docker compose up -d
Production Considerations
For a production environment, you should:

Set up SSL/TLS for secure connections:
Generate SSL certificates (or use Let's Encrypt)
Configure Apache to use HTTPS
Configure firewall rules:
Only expose necessary ports (80, 443) to the internet
Block direct access to Docker container ports (3000, 5000, 5432)
Set up regular backups:
Backup your PostgreSQL database regularly
Backup your Apache configuration files




