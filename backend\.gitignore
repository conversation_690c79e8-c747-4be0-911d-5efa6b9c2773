# dependencies
/node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# logs
logs
*.log

# runtime data
pids
*.pid
*.seed
*.pid.lock

# directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache