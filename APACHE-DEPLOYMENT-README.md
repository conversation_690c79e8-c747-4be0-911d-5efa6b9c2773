# Apache Deployment for Docker Containers

This directory contains files to help you deploy your Docker containers with Apache HTTP Server on Windows.

## Quick Start

1. **Run the setup script** (requires Administrator privileges):
   - Double-click `setup-apache.bat` or
   - Right-click `setup-apache.ps1` and select "Run with PowerShell"

2. **Access your application**:
   - Open a browser and navigate to `http://localhost`

## Manual Setup

If you prefer to set up Apache manually, follow the detailed instructions in `apache-docker-deployment-guide.md`.

## Files Included

- `setup-apache.ps1`: PowerShell script to automate Apache installation and configuration
- `setup-apache.bat`: Batch file wrapper for the PowerShell script
- `apache-docker-deployment-guide.md`: Detailed step-by-step guide
- `apache-config/httpd-vhosts.conf`: Apache virtual host configuration for HTTP
- `apache-config/httpd-ssl.conf`: Apache virtual host configuration for HTTPS (production use)

## Production Deployment

For production deployment, you should:

1. Replace `yourdomain.com` in `apache-config/httpd-ssl.conf` with your actual domain name
2. Obtain SSL certificates for your domain
3. Configure Apache to use SSL/TLS
4. Set up proper firewall rules

See the "Configure Apache for Production" section in `apache-docker-deployment-guide.md` for more details.

## Troubleshooting

If you encounter issues, check:

1. Apache error logs at `C:\Apache24\logs\error.log`
2. Docker container logs with `docker logs [container_name]`
3. Make sure all required ports are available (80, 443, 3000, 5000)

## Support

If you need further assistance, please refer to:

- Apache HTTP Server documentation: https://httpd.apache.org/docs/2.4/
- Docker documentation: https://docs.docker.com/
