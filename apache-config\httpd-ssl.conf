<VirtualHost *:80>
    ServerName yourdomain.com
    ServerAdmin <EMAIL>
    
    # Redirect all HTTP traffic to HTTPS
    Redirect permanent / https://yourdomain.com/
</VirtualHost>

<VirtualHost *:443>
    ServerName yourdomain.com
    ServerAdmin <EMAIL>
    DocumentRoot "C:/Apache24/htdocs"
    
    # SSL Configuration
    SSLEngine on
    SSLCertificateFile "C:/Apache24/conf/ssl/yourdomain.crt"
    SSLCertificateKeyFile "C:/Apache24/conf/ssl/yourdomain.key"
    
    # Proxy for frontend (React app)
    ProxyPass / http://localhost:3000/
    ProxyPassReverse / http://localhost:3000/
    
    # Proxy for backend API
    ProxyPass /api http://localhost:5000/
    ProxyPassReverse /api http://localhost:5000/
    
    ErrorLog "logs/yourdomain-error.log"
    CustomLog "logs/yourdomain-access.log" common
</VirtualHost>
