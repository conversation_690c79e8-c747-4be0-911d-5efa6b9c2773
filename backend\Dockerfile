FROM node:18-alpine

# Set the working directory inside the container
WORKDIR /usr/src/app

# Copy package.json and package-lock.json (or yarn.lock)
# This step is separate to leverage <PERSON><PERSON>'s layer caching.
# If these files don't change, <PERSON><PERSON> won't re-run npm install unnecessarily.
COPY package*.json ./

# Install dependencies
# Use npm ci for production builds for deterministic installs
RUN npm ci --only=production

# Copy the rest of your backend application code
COPY . .

# Expose the port your backend listens on (5000)
EXPOSE 5000

# Define the command to run your application
# This assumes your entry file is index.js
CMD [ "node", "index.js" ]