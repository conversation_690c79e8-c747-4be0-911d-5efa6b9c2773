
//styling
//this demonstrates how to import css file at parent directory from css folder
import '../css/Home.css'; 
import { Fragment, useState} from 'react';


const Home = ({ item, onUpdate }) => {
    // Default item to an empty object if it's undefined
    const safeItem = item || {};
    const [description, setDescription] = useState(safeItem.description || '');

    //edit description
    const updateDescription = async (e) => {
            e.preventDefault();
            if (!safeItem.id) {
                console.error("No item ID available");
                return;
            }
            try{
                const body = { description }
                const response = await fetch(`http://localhost:5000/test-put/${safeItem.id}`, {
                  method: "PUT",
                  headers: { "Content-Type": "application/json" },
                  body: JSON.stringify(body)
                });

                if (response.ok) {
                    console.log("Update successful:", body);

                    // Close the modal after successful update
                    const modal = document.getElementById(`id${safeItem.id}`);
                    if (modal) {
                        // Use Bootstrap's modal hide method if available, otherwise use jQuery
                        if (window.$ && window.$.fn.modal) {
                            window.$(`#id${safeItem.id}`).modal('hide');
                        } else {
                            modal.style.display = 'none';
                            modal.classList.remove('show');
                            document.body.classList.remove('modal-open');
                            // Remove backdrop if it exists
                            const backdrop = document.querySelector('.modal-backdrop');
                            if (backdrop) {
                                backdrop.remove();
                            }
                        }
                    }

                    // Call the callback function to notify parent component
                    if (onUpdate && typeof onUpdate === 'function') {
                        onUpdate();
                    }
                } else {
                    console.error("Update failed with status:", response.status);
                }

            }catch(error){
              console.error("Error updating description:", error.message);
            }
    }

    // Only render the edit button and modal if we have an item
    return ( 
      <Fragment> 
        {safeItem.id ? (
          <>
            <button type="button" className="btn btn-primary btn-warning" data-toggle="modal" data-target={`#id${safeItem.id}`}>
              Edit
            </button>

            <div className="modal" id={`id${safeItem.id}`}>
              <div className="modal-dialog">
                <div className="modal-content">


                  <div className="modal-header">
                    <h4 className="modal-title">Edit Todo</h4>
                    <button type="button" className="close" data-dismiss="modal">&times;</button>
                  </div>

               {/**
                  *  value={description} set it first before onChange
                 * onChange is used to update the text in the input 
                */}
                  <div className="modal-body">
                    <input type="text" className="form-control" value={description} onChange={e => setDescription(e.target.value)} />
                  </div>


                  <div className="modal-footer">
                    <button type="button" className="btn btn-success" onClick={updateDescription}>Update</button>
                    <button type="button" className="btn btn-secondary" data-dismiss="modal" >Cancel</button>
                  </div>

                </div>
              </div>
            </div>
          </>
        ) : (
          <p>Welcome to the Home page</p>
        )}
      </Fragment>
    )
  };
  
  export default Home;
