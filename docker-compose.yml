version: '3.8' # Specify the Docker Compose file format version

services:

  # PostgreSQL Database Service
  db:
    image: postgres:15-alpine # Use an official PostgreSQL image (alpine is lightweight)
    container_name: pern_db
    # Map host port 5432 to container port 5432 (Optional for production, useful for local debugging)
    # In production, you typically don't expose the DB port directly to the host or internet.
    # The backend container will connect to it via the internal Docker network.
    ports:
       - "5432:5432"
    environment:
      # These environment variables configure the PostgreSQL container on startup
      POSTGRES_USER: postgres       # <-- CHANGE THIS to a strong, unique user
      POSTGRES_PASSWORD: Postgresql143! # <-- <PERSON>ANGE THIS to a strong, unique password
      POSTGRES_DB: pern_todo            # <-- CHANGE THIS if your DB name is different
    volumes:
      # Persist database data outside the container.
      # This ensures your data is not lost if the container is removed or updated.
      - postgres_data:/var/lib/postgresql/data
      # Mount the initialization script
      - ./database-init.sql:/docker-entrypoint-initdb.d/database-init.sql
    restart: always # Automatically restart the container if it fails

  # Backend API Service (Node.js/Express)
  backend:
    # Build the image using the Dockerfile in the ./backend directory
    build: ./backend
    container_name: pern_backend
    # Ensure the database is running before starting the backend
    depends_on:
      - db
    # Map host port 5000 to container port 5000 (Optional for production, useful for local testing)
    # In production, Apache/Nginx will typically proxy requests to this container internally.
    ports:
      - "5000:5000"
    environment:
      # These environment variables are passed to your Node.js application (index.js)
      # The 'db' hostname works because Docker Compose creates a network where services
      # can reach each other using their service names.
      DB_USER: postgres       # <-- Use the same user as defined for the 'db' service
      DB_PASSWORD: Postgresql143! # <-- Use the same password as defined for the 'db' service
      DB_HOST: db                 # <-- Use the service name of the database container
      DB_NAME: pern_todo            # <-- Use the same DB name as defined for the 'db' service
      DB_PORT: 5432               # <-- Use the default PostgreSQL port
      PORT: 5000                  # <-- The port your Express app listens on inside the container
    # Optional: Mount the backend code for easier development (remove for production builds)
    # volumes:
    #   - ./backend:/usr/src/app
    #   - /usr/src/app/node_modules # Exclude node_modules from host mount
    restart: always # Automatically restart the container if it fails

  # Frontend Service (React served by Nginx)
  frontend:
    # Build the image using the Dockerfile in the ./frontend directory
    build: ./frontend
    container_name: pern_frontend
    # Map host port 3000 to container port 80 (Nginx default)
    # This makes your frontend accessible on your host machine at http://localhost:3000
    # For production, you'd map host port 80 and 443 here.
    ports:
      - "3000:80" # Map host port 3000 to Nginx container port 80
      # For production, you'd typically map 80:80 and 443:443
      # - "80:80"
      # - "443:443" # Requires SSL setup in Nginx config
    # Optional: Ensure backend is running before frontend (less critical as frontend is static)
    # depends_on:
    #   - backend
    restart: always # Automatically restart the container if it fails

# Define named volumes for data persistence
volumes:
  postgres_data: # This volume will be managed by Docker to store PostgreSQL data
  # You could also define a volume here for Nginx logs if needed
  # nginx_logs:
