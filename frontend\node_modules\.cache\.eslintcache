[{"C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\Myapp.js": "3", "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\pages\\Blogs.js": "4", "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\pages\\Contact.js": "5", "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\pages\\Layout.js": "6", "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\pages\\NoPage.js": "7", "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\pages\\Home.js": "8", "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\components\\inputToDo.js": "9", "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\reportWebVitals.js": "10"}, {"size": 584, "mtime": 1747235029743, "results": "11", "hashOfConfig": "12"}, {"size": 233, "mtime": 1747234927666, "results": "13", "hashOfConfig": "12"}, {"size": 1555, "mtime": 1742392979711, "results": "14", "hashOfConfig": "12"}, {"size": 955, "mtime": 1748836105822, "results": "15", "hashOfConfig": "12"}, {"size": 2678, "mtime": 1748838935835, "results": "16", "hashOfConfig": "12"}, {"size": 6151, "mtime": 1742968612962, "results": "17", "hashOfConfig": "12"}, {"size": 84, "mtime": 1742391528072, "results": "18", "hashOfConfig": "12"}, {"size": 4111, "mtime": 1748840128308, "results": "19", "hashOfConfig": "12"}, {"size": 143, "mtime": 1747237502173, "results": "20", "hashOfConfig": "12"}, {"size": 455, "mtime": 1747234735982, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1kkckd7", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\index.js", ["52"], [], "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\App.js", ["53"], [], "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\Myapp.js", ["54"], [], "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\pages\\Blogs.js", [], [], "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\pages\\Contact.js", [], [], "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\pages\\Layout.js", [], [], "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\pages\\NoPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\components\\inputToDo.js", [], [], "C:\\Users\\<USER>\\Desktop\\Full Stack Practice\\frontend\\src\\reportWebVitals.js", [], [], {"ruleId": "55", "severity": 1, "message": "56", "line": 4, "column": 8, "nodeType": "57", "messageId": "58", "endLine": 4, "endColumn": 11}, {"ruleId": "55", "severity": 1, "message": "59", "line": 5, "column": 8, "nodeType": "57", "messageId": "58", "endLine": 5, "endColumn": 17}, {"ruleId": "55", "severity": 1, "message": "60", "line": 2, "column": 8, "nodeType": "57", "messageId": "58", "endLine": 2, "endColumn": 16}, "no-unused-vars", "'App' is defined but never used.", "Identifier", "unusedVar", "'inputToDo' is defined but never used.", "'ReactDOM' is defined but never used."]