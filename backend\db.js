//require pg to get pool module
const Pool = require('pg').Pool;

//create instance
//define db param for connection
const pool = new Pool({
    user: process.env.DB_USER || "postgres",
    password: process.env.DB_PASSWORD || "Postgresql143!",
    host: process.env.DB_HOST || "Localhost", // Use 'db' as the hostname in Docker
    port: parseInt(process.env.DB_PORT || "5432"),
    database: process.env.DB_NAME || "pern_todo",
    // Add connection timeout and retry settings
    connectionTimeoutMillis: 5000, // 5 seconds
    idleTimeoutMillis: 30000, // 30 seconds
    max: 20 // Maximum number of clients in the pool
});

// Log connection parameters (without password for security)
console.log('PostgreSQL connection parameters:', {
    user: process.env.DB_USER || "postgres",
    host: process.env.DB_HOST || "db",
    port: parseInt(process.env.DB_PORT || "5432"),
    database: process.env.DB_NAME || "pern_todo"
});

// Add error handling for the pool
pool.on('error', (err) => {
    console.error('Unexpected error on idle client', err);
    process.exit(-1);
});

// Test the connection on startup
pool.query('SELECT NOW()', (err, res) => {
    if (err) {
        console.error('Error connecting to PostgreSQL:', err);
    } else {
        console.log('Successfully connected to PostgreSQL at:', res.rows[0].now);
    }
});

module.exports = pool;



/*
1. Restart your Docker containers

# Stop all running containers
docker compose down

# Start containers in the background
docker compose up -d


2. Check if containers are running

docker ps

You should see your containers listed (db, backend, frontend).

3. Check backend logs for connection status

# View logs from the backend container
docker logs pern_backend

Look for the connection message we added to db.js:

Success: "Successfully connected to PostgreSQL at: [timestamp]"
Failure: "Error connecting to PostgreSQL: [error message]"


4. Check database container directly
# Connect to PostgreSQL inside the container
docker exec -it pern_db psql -U postgres -d pern_todo

# Once connected, you can run:
\dt  # List tables
SELECT NOW();  # Test query
\q   # Quit

5. Check network connectivity between containers

# Connect to the backend container
docker exec -it pern_backend sh

# From inside the backend container, test connection to db
ping db
# Or use a more direct test
nc -zv db 5432



If you're still having issues, check the database logs:

docker logs pern_db



*/