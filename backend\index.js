//module
/*
const express = require('express');
const cors = require('cors');

//instance
const app = express();

//port
const PORT = 5000;


//middleware

app.use(cors())
app.use(express.json())


app.get('/test-get', (req, res) => {
    //res.send('Hello from backend!');
    res.status(200).json({
        message: "Get request successful",
        data: "Hello from backend!"
    });
})


app.post('/test-post', (req, res) => {
    try {
        // Simulate a potential error for demonstration
        // For example, if req.body.name is expected but not provided,
        // you might throw an error or have an operation fail.
         if (!req.body || !req.body.name) {
             throw new Error("Name is required in the request body.");
         }

        console.log('Data received from client: ', req.body);

        // Send a response back to Postman
        res.status(200).json({
            message: 'POST request successful! Data received.',
            dataReceived: req.body // Echo back the data received
        });
    } catch (error) {
        console.error('Error processing POST request:', error.message);
        // Send an error response
        res.status(500).json({
            message: 'Error processing request.',
            error: error.message // It's often good to send a generic message in production
                                 // and log the detailed error.message for debugging.
        });
    }
});




app.put('/test-put/:id', (req, res) => {
    const itemId = req.params.id; // Access the 'id' from the URL path
    const updatedData = req.body;   // Get the data sent in the request body

    console.log(`PUT request to update item with ID: ${itemId}`);
    console.log('Data received for update: ', updatedData);

    // In a real application, you would:
    // 1. Find the item with 'itemId' in your database or data store.
    // 2. Update its properties with 'updatedData'.
    // 3. Save the changes.

    // For this example, we'll just echo back the received information.
    res.status(200).json({
        message: `Item with ID ${itemId} would be updated.`,
        itemId: itemId,
        dataSent: updatedData
    });
});




app.delete('/test-delete/:id', (req, res) => {
    const itemId = req.params.id; // Access the 'id' from the URL path

    console.log(`DELETE request for item with ID: ${itemId}`);

    // In a real application, you would:
    // 1. Find the item with 'itemId' in your database or data store.
    // 2. Delete it.

    // For this example, we'll just confirm the action.
    res.status(200).json({
        message: `Item with ID ${itemId} would be deleted.`,
        itemId: itemId
    });
    // Alternatively, for DELETE, a 204 No Content status is also very common
    // if you don't need to send any data back in the body:
    // res.status(204).send();
});



//start server

app.listen(PORT, () => {
    console.log(`Server is working on port ${PORT}`);
} )
*/


































































































































/* 


//module
const express = require('express');
const cors = require('cors');
const pool = require('./db');

//create instance
const app = express();

//set port
const PORT = 5000;

//middleware
app.use(cors());
app.use(express.json());

//post request
app.post('/test-post', async(req, res) => {
    try{
        // Log the entire request body to see what's being received
        console.log('Received data from client:', req.body);

        const { description } = req.body;

        // Validate that description exists and is a non-empty string
        if (!description || typeof description !== 'string' || description.trim() === "") {
            // It's better to return a 400 Bad Request if input is missing/invalid
            return res.status(400).json({ message: "Description is required and must be a non-empty string." });
        }

        // Log the query we're about to execute
        console.log('Executing query:', "INSERT INTO todo (description) VALUES($1) RETURNING *", [description]);

        //database connection
        const newToDo = await pool.query("INSERT INTO todo (description) VALUES($1) RETURNING *", [description]);

        // Log the query result
        console.log('Query result:', newToDo);
        console.log('Query rows:', newToDo.rows);
        console.log('First row:', newToDo.rows[0]);

        // Respond with 201 Created status for successful resource creation
        // And it's good practice to return the created resource
        res.status(201).json({
            message: "POST request successful!",
            // Send back the actual data that was processed and saved
            dataSaved: newToDo.rows && newToDo.rows.length > 0 ? newToDo.rows[0] : null
        });

    }catch(error){
        console.error("Error processing POST request", error.message);
        console.error("Error stack:", error.stack);

        res.status(500).json({
            message: "Error processing POST request",
            error: error.message
        });
    }
})



//get request
app.get('/test-get', async (req, res) => {
    try{
        //console.log('Get request successful!', data); //"data" this will cause an error and will be handled by catch block - Error GET request:  data is not defined

        const allToDo = await pool.query('SELECT * FROM todo');

        console.log('Pulled up all todo info! ', allToDo.rows);


        res.status(200).json({
            message : "Get request successful!",
            data : allToDo.rows
        })
    }catch(error){
        console.log("Error GET request: ", error.message);

        res.status(500).json({
            message : "Error GET request:",
            error : error.message
        })

    }
})


//get request with an id
app.get('/test-get/:id', async (req, res) => {
    try{
        const { id } = req.params;

        const todo = await pool.query("SELECT * FROM todo WHERE id = $1", [id]);

        // 3. Check if the item was found
        if (todo.rows.length === 0) {
            console.log(`No item found with id: ${id}`);
            return res.status(404).json({ message: "Todo item not found." });
        }

        console.log(`Retrieved item with id: ${id} successfully!`);

        res.status(200).json({
            message : 'Retrieved item successfully!',
            data : todo.rows[0]
        })



    }catch(error){
        console.error('Error retrieving an item:', error.message);

        res.status(500).json({
            message : 'Error retrieving an item!',
            error: error.message
        })


    }
})







//put request

app.put('/test-put/:id', async (req, res) => {
    try{

        const {id} = req.params;
        const {description} = req.body;

        console.log('Update item id: ', id );

        const updatedToDo = await pool.query('UPDATE todo SET description = $1 WHERE id = $2', [description, id])

        console.log(`Successfully updated todo data with id ${id}:`, description);



        res.status(200).json({
            message : `Updated item with id: ${id}`,
            dataSent: description,
            itemId: id
        })


    }catch(error){
        console.error('Error updating item: ', error.message);

        res.status(500).json({
            message: 'Error updating item',
            itemId: id
        })

    }
})





//delete request
//testing error block

app.delete('/test-delete/:id', async (req, res) => {
    try{
        //const itemId = req.params.id;  // Uncomment this line

        const {id} = req.params;

        const deleteToDo = await pool.query("DELETE FROM todo WHERE id = $1", [id]);


        console.log('Successful request to delete item with id:', id);
        console.log(deleteToDo);

        res.status(200).json({
            message : "Deleted successfully!",
            itemId : id
        })
    }catch(error){
        console.error('Unable to delete:', error.message)
        res.status(500).json({
            message : "Unable to delete item",
            error: error.message
        })
    }
})










//run server
app.listen(PORT, () => {
    console.log(`server is listening on port ${PORT}`);
})


*/

































//module
const express = require('express');
const cors = require('cors');
const pool = require('./db');

//create instance

const app = express();


//port
const PORT = 5000;
//middleware

app.use(cors());
app.use(express.json());


//post request

app.post('/test-post', async(req, res) => {
    try{

        //editor this will log without database
        console.log('Accepted request from client:', req.body);



        //database
        const {description} = req.body;

        //post to db
        //database connection
        //MAKE SURE THE description is define in postman/react: ex. { "description" : "gwapo ka" }
        const newToDo = await pool.query("INSERT INTO todo (description) VALUES($1) RETURNING *", [description]);

        // Log the query result
        console.log('Query result:', newToDo.rows[0]);



        //postman
        res.status(201).json({
            message : 'POST request successful!'
        })

    }catch(error){
        console.error('Error:', error.message)
    }
})





//get request

app.get('/test-get', async(req, res) => {
    try{
        //database access only
        const allData = await pool.query('SELECT * FROM todo');
        console.log('All data from todo table: ', allData.rows);

        res.status(200).json({
            message : "Get request successful!",
            data: allData.rows
        })


    }catch(error){
        console.error(error.mesage);
    }
})



app.get('/test-get/:id', async (req, res) => {
    try{
        //db only
        const { id } = req.params;

        const oneItemOnly = await pool.query( 'SELECT * FROM todo WHERE id = $1', [id]);

        //editor
        console.log('Get request successful: ', oneItemOnly.rows[0])
      
        //postman
        res.status(200).json({
            message : "Get request successful:",
            data : oneItemOnly.rows[0]
        })


    }catch(error){
        console.log(error.message);
    }
})




app.put('/test-put/:id', async (req, res) => {
    try{
        //id
        const {id} = req.params;
        const {description} = req.body;

        const updateItem = await pool.query('UPDATE todo SET description = $1 WHERE id = $2', [description, id]);
        console.log('Successfully updated item!', updateItem);


        res.status(200).json({
            message : 'Successfully updated item!', 
            dataSent : description,
            itemId: id
        })

    }catch(error){
        console.error(error.message);
    }
})


app.delete('/test-delete/:id', async (req, res) => {
    try{
        const {id} = req.params;
        
        const deleteItem = await pool.query('DELETE FROM todo WHERE id = $1', [id]);
        console.log('Item deleted!', deleteItem);

        res.status(200).json({
            message : 'Item deleted successfully!',
            itemId : id
        })

    }catch(error){
        console.error(error.message);
    }
})







//start server
app.listen(PORT, () => {
    console.log('Server is listening on port 5000 gwapo');
})