{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Full Stack Practice\\\\frontend\\\\src\\\\pages\\\\Home.js\",\n  _s = $RefreshSig$();\n//styling\n//this demonstrates how to import css file at parent directory from css folder\nimport '../css/Home.css';\nimport { Fragment, useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = ({\n  item\n}) => {\n  _s();\n  const [description, setDescription] = useState(item.description);\n\n  //edit description \n\n  const updateDescription = async e => {\n    e.preventDefault();\n    try {\n      const body = {\n        description\n      };\n      const response = await fetch(`http://localhost:5000/test-put/${item.id}`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(body)\n      });\n      console.log(response);\n    } catch (error) {\n      console.error(error.message);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      type: \"button\",\n      class: \"btn btn-primary btn-warning\",\n      \"data-toggle\": \"modal\",\n      \"data-target\": `#id${item.id}`,\n      children: \"Edit\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 11\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      class: \"modal\",\n      id: `id${item.id}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        class: \"modal-dialog\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          class: \"modal-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"modal-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              class: \"modal-title\",\n              children: \"Input Todo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              class: \"close\",\n              \"data-dismiss\": \"modal\",\n              onClick: e => updateDescription(e),\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"modal-body\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"form-control\",\n              value: description,\n              onChange: e => setDescription(e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            class: \"modal-footer\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              class: \"btn btn-warning\",\n              \"data-dismiss\": \"modal\",\n              children: \"Edit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              class: \"btn btn-danger\",\n              \"data-dismiss\": \"modal\",\n              children: \"Close\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 11\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 7\n  }, this);\n};\n_s(Home, \"i+adegVe9TGkm+SGG167KNmkABU=\");\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["Fragment", "useState", "jsxDEV", "_jsxDEV", "Home", "item", "_s", "description", "setDescription", "updateDescription", "e", "preventDefault", "body", "response", "fetch", "id", "method", "headers", "JSON", "stringify", "console", "log", "error", "message", "children", "type", "class", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "className", "value", "onChange", "target", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Full Stack Practice/frontend/src/pages/Home.js"], "sourcesContent": ["\r\n//styling\r\n//this demonstrates how to import css file at parent directory from css folder\r\nimport '../css/Home.css'; \r\nimport { Fragment, useState} from 'react';\r\n\r\n\r\nconst Home = ( {item} ) => {\r\n    const [description, setDescription] = useState(item.description);\r\n\r\n    //edit description \r\n\r\n    const updateDescription = async (e) => {\r\n            e.preventDefault();\r\n            try{\r\n                const body = { description }\r\n                const response = await fetch(`http://localhost:5000/test-put/${item.id}`, {\r\n                  method: \"PUT\",\r\n                  headers: { \"Content-Type\": \"application/json\" },\r\n                  body: JSON.stringify(body)\r\n                });\r\n\r\n                console.log(response);\r\n\r\n            }catch(error){\r\n              console.error(error.message);\r\n            }\r\n    }\r\n\r\n\r\n\r\n    return ( \r\n      <Fragment> \r\n    \r\n          <button type=\"button\" class=\"btn btn-primary btn-warning\" data-toggle=\"modal\" data-target={`#id${item.id}`}>\r\n            Edit\r\n          </button>\r\n\r\n          {/**\r\n           * id = id7\r\n           */}\r\n          <div class=\"modal\" id={`id${item.id}`}>\r\n            <div class=\"modal-dialog\">\r\n              <div class=\"modal-content\">\r\n\r\n\r\n                <div class=\"modal-header\">\r\n                  <h4 class=\"modal-title\">Input Todo</h4>\r\n                  <button type=\"button\" class=\"close\" data-dismiss=\"modal\"\r\n                    onClick = { e => updateDescription(e)}\r\n                  \r\n                  >&times;</button>\r\n                </div>\r\n\r\n             {/**\r\n                *  value={description} set it first before onChange\r\n               * onChange is used to update the text in the input \r\n              */}\r\n                <div class=\"modal-body\">\r\n                  <input type=\"text\" className=\"form-control\" value={description} onChange={e => setDescription(e.target.value)} />\r\n                </div>\r\n\r\n\r\n                <div class=\"modal-footer\">\r\n                  <button type=\"button\" class=\"btn btn-warning\" data-dismiss=\"modal\">Edit</button>\r\n                  <button type=\"button\" class=\"btn btn-danger\" data-dismiss=\"modal\">Close</button>\r\n                </div>\r\n\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n\r\n\r\n\r\n\r\n      </Fragment>\r\n    )\r\n  };\r\n  \r\n  export default Home;"], "mappings": ";;AACA;AACA;AACA,OAAO,iBAAiB;AACxB,SAASA,QAAQ,EAAEC,QAAQ,QAAO,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG1C,MAAMC,IAAI,GAAGA,CAAE;EAACC;AAAI,CAAC,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGP,QAAQ,CAACI,IAAI,CAACE,WAAW,CAAC;;EAEhE;;EAEA,MAAME,iBAAiB,GAAG,MAAOC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAG;MACC,MAAMC,IAAI,GAAG;QAAEL;MAAY,CAAC;MAC5B,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,kCAAkCT,IAAI,CAACU,EAAE,EAAE,EAAE;QACxEC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CL,IAAI,EAAEM,IAAI,CAACC,SAAS,CAACP,IAAI;MAC3B,CAAC,CAAC;MAEFQ,OAAO,CAACC,GAAG,CAACR,QAAQ,CAAC;IAEzB,CAAC,QAAMS,KAAK,EAAC;MACXF,OAAO,CAACE,KAAK,CAACA,KAAK,CAACC,OAAO,CAAC;IAC9B;EACR,CAAC;EAID,oBACEpB,OAAA,CAACH,QAAQ;IAAAwB,QAAA,gBAELrB,OAAA;MAAQsB,IAAI,EAAC,QAAQ;MAACC,KAAK,EAAC,6BAA6B;MAAC,eAAY,OAAO;MAAC,eAAa,MAAMrB,IAAI,CAACU,EAAE,EAAG;MAAAS,QAAA,EAAC;IAE5G;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAKT3B,OAAA;MAAKuB,KAAK,EAAC,OAAO;MAACX,EAAE,EAAE,KAAKV,IAAI,CAACU,EAAE,EAAG;MAAAS,QAAA,eACpCrB,OAAA;QAAKuB,KAAK,EAAC,cAAc;QAAAF,QAAA,eACvBrB,OAAA;UAAKuB,KAAK,EAAC,eAAe;UAAAF,QAAA,gBAGxBrB,OAAA;YAAKuB,KAAK,EAAC,cAAc;YAAAF,QAAA,gBACvBrB,OAAA;cAAIuB,KAAK,EAAC,aAAa;cAAAF,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvC3B,OAAA;cAAQsB,IAAI,EAAC,QAAQ;cAACC,KAAK,EAAC,OAAO;cAAC,gBAAa,OAAO;cACtDK,OAAO,EAAKrB,CAAC,IAAID,iBAAiB,CAACC,CAAC,CAAE;cAAAc,QAAA,EAEvC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eAMN3B,OAAA;YAAKuB,KAAK,EAAC,YAAY;YAAAF,QAAA,eACrBrB,OAAA;cAAOsB,IAAI,EAAC,MAAM;cAACO,SAAS,EAAC,cAAc;cAACC,KAAK,EAAE1B,WAAY;cAAC2B,QAAQ,EAAExB,CAAC,IAAIF,cAAc,CAACE,CAAC,CAACyB,MAAM,CAACF,KAAK;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9G,CAAC,eAGN3B,OAAA;YAAKuB,KAAK,EAAC,cAAc;YAAAF,QAAA,gBACvBrB,OAAA;cAAQsB,IAAI,EAAC,QAAQ;cAACC,KAAK,EAAC,iBAAiB;cAAC,gBAAa,OAAO;cAAAF,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChF3B,OAAA;cAAQsB,IAAI,EAAC,QAAQ;cAACC,KAAK,EAAC,gBAAgB;cAAC,gBAAa,OAAO;cAAAF,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAMA,CAAC;AAEf,CAAC;AAACxB,EAAA,CAvEEF,IAAI;AAAAgC,EAAA,GAAJhC,IAAI;AAyER,eAAeA,IAAI;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}