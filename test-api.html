<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input, button {
            padding: 8px;
        }
        button {
            cursor: pointer;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
        }
        #response {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <h1>Test PostgreSQL Connection</h1>
    
    <div class="form-group">
        <label for="description">Description:</label>
        <input type="text" id="description" name="description" value="Test from browser">
    </div>
    
    <button onclick="testPost()">Test POST</button>
    <button onclick="testGet()">Test GET</button>
    
    <div id="response">
        <p>Response will appear here...</p>
    </div>
    
    <script>
        async function testPost() {
            const description = document.getElementById('description').value;
            const responseDiv = document.getElementById('response');
            
            try {
                const response = await fetch('http://localhost:5000/test-post', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ description })
                });
                
                const data = await response.json();
                responseDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                responseDiv.innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }
        
        async function testGet() {
            const responseDiv = document.getElementById('response');
            
            try {
                const response = await fetch('http://localhost:5000/test-get');
                const data = await response.json();
                responseDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            } catch (error) {
                responseDiv.innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
