# Deploying Docker Containers with Apache on Windows

This guide will walk you through the process of deploying your Docker containers with Apache HTTP Server on Windows for production use.

## Prerequisites

- Windows 10/11 or Windows Server
- Docker Desktop for Windows (installed and running)
- Administrator access to your Windows machine

## Step 1: Install Apache HTTP Server

1. **Download Apache HTTP Server for Windows (64-bit version)**:
   - Download the latest Apache HTTP Server from Apache Lounge: https://www.apachelounge.com/download/VS17/binaries/httpd-2.4.63-250207-win64-VS17.zip

2. **Install the Visual C++ Redistributable (required for Apache)**:
   - Download and install the Visual C++ Redistributable: https://aka.ms/vs/17/release/VC_redist.x64.exe

3. **Extract Apache to a location on your system**:
   ```
   C:\Apache24
   ```

4. **Open a Command Prompt as Administrator and install Apache as a Windows service**:
   ```
   cd C:\Apache24\bin
   httpd.exe -k install
   ```

5. **Start the Apache service**:
   ```
   httpd.exe -k start
   ```

6. **Verify Apache is running** by opening a browser and navigating to:
   ```
   http://localhost
   ```
   You should see the Apache default page.

## Step 2: Configure Apache as a Reverse Proxy

1. **Enable required Apache modules** by editing `C:\Apache24\conf\httpd.conf`:
   - Uncomment (remove the # at the beginning) the following lines:
   ```
   LoadModule proxy_module modules/mod_proxy.so
   LoadModule proxy_http_module modules/mod_proxy_http.so
   LoadModule proxy_wstunnel_module modules/mod_proxy_wstunnel.so
   ```

2. **Copy the virtual host configuration file** from this project:
   - Copy `apache-config/httpd-vhosts.conf` to `C:\Apache24\conf\extra\httpd-vhosts.conf`

3. **Make sure the virtual hosts configuration is included** in the main config by checking that this line is uncommented in `C:\Apache24\conf\httpd.conf`:
   ```
   Include conf/extra/httpd-vhosts.conf
   ```

4. **Restart Apache** to apply the changes:
   ```
   httpd.exe -k restart
   ```

## Step 3: Start Your Docker Containers

1. **Start your Docker containers**:
   ```
   docker compose up -d
   ```

2. **Verify that your containers are running**:
   ```
   docker ps
   ```
   You should see your containers listed (db, backend, frontend).

## Step 4: Test the Deployment

1. **Open a browser and navigate to**:
   ```
   http://localhost
   ```
   You should see your React frontend application.

2. **Test the backend API** by navigating to:
   ```
   http://localhost/api/test-get
   ```
   You should receive a JSON response from your backend.

## Step 5: Configure Apache for Production (Optional)

For a production environment, you might want to:

1. **Set up SSL/TLS for secure connections**:
   - Generate SSL certificates (or use Let's Encrypt)
   - Configure Apache to use HTTPS

2. **Create a more specific virtual host configuration**:
   ```apache
   <VirtualHost *:80>
       ServerName yourdomain.com
       ServerAdmin <EMAIL>
       
       # Redirect all HTTP traffic to HTTPS
       Redirect permanent / https://yourdomain.com/
   </VirtualHost>

   <VirtualHost *:443>
       ServerName yourdomain.com
       ServerAdmin <EMAIL>
       DocumentRoot "C:/Apache24/htdocs"
       
       # SSL Configuration
       SSLEngine on
       SSLCertificateFile "C:/Apache24/conf/ssl/yourdomain.crt"
       SSLCertificateKeyFile "C:/Apache24/conf/ssl/yourdomain.key"
       
       # Proxy for frontend (React app)
       ProxyPass / http://localhost:3000/
       ProxyPassReverse / http://localhost:3000/
       
       # Proxy for backend API
       ProxyPass /api http://localhost:5000/
       ProxyPassReverse /api http://localhost:5000/
       
       ErrorLog "logs/yourdomain-error.log"
       CustomLog "logs/yourdomain-access.log" common
   </VirtualHost>
   ```

## Troubleshooting

1. **Apache won't start**:
   - Check the error logs at `C:\Apache24\logs\error.log`
   - Ensure all required modules are properly loaded
   - Verify the syntax of your configuration files with `httpd.exe -t`

2. **Cannot connect to Docker containers**:
   - Ensure Docker containers are running with `docker ps`
   - Check container logs with `docker logs [container_name]`
   - Verify network connectivity between Apache and Docker

3. **Changes not reflecting**:
   - Restart Apache with `httpd.exe -k restart`
   - Restart Docker containers with `docker compose down` followed by `docker compose up -d`

## Maintenance

1. **Updating Docker containers**:
   ```
   docker compose down
   docker compose pull
   docker compose up -d
   ```

2. **Backing up PostgreSQL data**:
   ```
   docker exec -t pern_db pg_dumpall -c -U postgres > backup.sql
   ```

3. **Restoring PostgreSQL data**:
   ```
   cat backup.sql | docker exec -i pern_db psql -U postgres
   ```

## Security Considerations

1. **Firewall Configuration**:
   - Only expose necessary ports (80, 443) to the internet
   - Block direct access to Docker container ports (3000, 5000, 5432)

2. **Regular Updates**:
   - Keep Apache, Docker, and your application containers updated
   - Apply security patches promptly

3. **SSL/TLS**:
   - Use strong SSL/TLS configurations
   - Regularly renew SSL certificates

## Conclusion

You have successfully deployed your Docker containers with Apache HTTP Server on Windows. This setup provides a production-ready environment for your PERN stack application.
