import React, {Fragment, useState} from 'react';



const Blogs = () => {

  //change state
  const [description, setDescription] = useState("");

  //on submit
  const onSubmitForm = async (e) => {
    e.preventDefault();
    try{
        const body = { description }
        const response = await fetch("http://localhost:5000/test-post", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(body)
        });

        console.log(response);

    }catch(error){
      console.error(error.message);
    }
  }

    return (
      <Fragment>
         <h1>Blog Articles Ni Teng</h1> 
         <p>Input To Do:</p>
        <form onSubmit={onSubmitForm}>
          <input type="text"  value={description} onChange={(e) => setDescription(e.target.value)}/>
          <button>add</button>
        </form>
      </Fragment>
    )
  };

  export default Blogs;