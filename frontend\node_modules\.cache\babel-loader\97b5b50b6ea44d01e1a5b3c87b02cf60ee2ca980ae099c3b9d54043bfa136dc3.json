{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Full Stack Practice\\\\frontend\\\\src\\\\pages\\\\Contact.js\",\n  _s = $RefreshSig$();\nimport React, { Fragment, useState, useEffect } from 'react';\n\n//get edit component from Home.js\nimport Home from \"./Home.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Contact = () => {\n  _s();\n  //todo is from the db table\n  const [todo, setTodo] = useState([]);\n\n  //delete todo\n\n  const deleteTodo = async id => {\n    // try{\n    //     const deleteTodo = await fetch(`http://localhost:5000/test-delete/${id}`, {method: 'DELETE'}) //this is a get request\n\n    //console.log(deleteTodo); test in the browser\n\n    try {\n      await fetch(`http://localhost:5000/test-delete/${id}`, {\n        method: 'DELETE'\n      });\n      // Update the state to reflect the deletion\n      setTodo(currentTodos => currentTodos.filter(item => item.id !== id));\n      //console.log(deleteTodo); test in the browser\n    } catch (error) {\n      console.error(error.message);\n    }\n  };\n  const getToDos = async () => {\n    try {\n      const response = await fetch('http://localhost:5000/test-get'); //this is a get request\n      const jsonData = await response.json();\n\n      //console.log(jsonData);      this will appear in the console\n      setTodo(jsonData.data); //this must be jsonData.data\n    } catch (error) {\n      console.error(error.message);\n    }\n  };\n  useEffect(() => {\n    getToDos();\n  }, []); //put [] to make sure this will return 1 array\n\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: [\" \", /*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Contact Me\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Basic Table\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"The .table class adds basic styling (light padding and only horizontal dividers) to a table:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"table\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Edit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Delete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children:\n          /*\r\n          Example of a static row (commented out):\r\n          <tr>\r\n            <td>John</td>\r\n            <td>Doe</td>\r\n            <td><EMAIL></td>\r\n          </tr>\r\n          */\n          todo.map(item => /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: item.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(Home, {\n                item: item,\n                onUpdate: getToDos\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-danger\",\n                onClick: () => deleteTodo(item.id),\n                children: \"Delete\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 17\n            }, this)]\n          }, item.id || item.description, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 8\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 7\n  }, this);\n};\n_s(Contact, \"4iPjF1Dk4tBAVioxtSV8zxHVK4I=\");\n_c = Contact;\nexport default Contact;\nvar _c;\n$RefreshReg$(_c, \"Contact\");", "map": {"version": 3, "names": ["React", "Fragment", "useState", "useEffect", "Home", "jsxDEV", "_jsxDEV", "Contact", "_s", "todo", "setTodo", "deleteTodo", "id", "fetch", "method", "currentTodos", "filter", "item", "error", "console", "message", "getToDos", "response", "jsonData", "json", "data", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "map", "description", "onUpdate", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Full Stack Practice/frontend/src/pages/Contact.js"], "sourcesContent": ["import React, {Fragment, useState, useEffect} from 'react';\r\n\r\n//get edit component from Home.js\r\nimport Home from \"./Home.js\";\r\n\r\n\r\nconst Contact = () => {\r\n    //todo is from the db table\r\n    const [todo, setTodo] = useState([]);\r\n\r\n    //delete todo\r\n    \r\n    const deleteTodo = async (id) => {\r\n       // try{\r\n       //     const deleteTodo = await fetch(`http://localhost:5000/test-delete/${id}`, {method: 'DELETE'}) //this is a get request\r\n           \r\n            //console.log(deleteTodo); test in the browser\r\n\r\n       try {\r\n            await fetch(`http://localhost:5000/test-delete/${id}`, { method: 'DELETE' });\r\n            // Update the state to reflect the deletion\r\n            setTodo(currentTodos => currentTodos.filter(item => item.id !== id)); \r\n            //console.log(deleteTodo); test in the browser\r\n\r\n        }catch(error){\r\n            console.error(error.message);\r\n        }\r\n    }\r\n\r\n    const getToDos = async () => {\r\n        try{\r\n            const response = await fetch('http://localhost:5000/test-get') //this is a get request\r\n            const jsonData = await response.json();\r\n\r\n            //console.log(jsonData);      this will appear in the console\r\n            setTodo(jsonData.data);   //this must be jsonData.data\r\n            \r\n        }catch(error){\r\n            console.error(error.message);\r\n        }\r\n    }\r\n\r\n    useEffect(() => {\r\n      getToDos();\r\n    },[]) //put [] to make sure this will return 1 array\r\n\r\n    return (\r\n      \r\n      <Fragment>\r\n        {\" \"}\r\n\r\n        <h1>Contact Me</h1>\r\n      <div className=\"container\">\r\n      <h2>Basic Table</h2>\r\n      <p>The .table class adds basic styling (light padding and only horizontal dividers) to a table:</p>            \r\n       <table className=\"table\">\r\n        <thead>\r\n          <tr>\r\n            <th>Description</th>\r\n            <th>Edit</th>\r\n            <th>Delete</th>\r\n          </tr>\r\n        </thead>\r\n\r\n        <tbody>\r\n \r\n\r\n\r\n           {\r\n            /*\r\n            Example of a static row (commented out):\r\n            <tr>\r\n              <td>John</td>\r\n              <td>Doe</td>\r\n              <td><EMAIL></td>\r\n            </tr>\r\n            */\r\n            todo.map(item => (\r\n              <tr key={item.id || item.description}>\r\n                <td>{item.description}</td>\r\n                <td><Home item={item} onUpdate={getToDos} /></td>\r\n                <td><button className=\"btn btn-danger\" onClick={() => deleteTodo(item.id)}>Delete</button></td>\r\n              </tr>\r\n            ))\r\n          }\r\n\r\n\r\n\r\n\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n      </Fragment>\r\n      \r\n\r\n    )\r\n  };\r\n  \r\nexport default Contact;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAGC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,QAAO,OAAO;;AAE1D;AACA,OAAOC,IAAI,MAAM,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG7B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB;EACA,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;;EAEpC;;EAEA,MAAMS,UAAU,GAAG,MAAOC,EAAE,IAAK;IAC9B;IACA;;IAEK;;IAEL,IAAI;MACC,MAAMC,KAAK,CAAC,qCAAqCD,EAAE,EAAE,EAAE;QAAEE,MAAM,EAAE;MAAS,CAAC,CAAC;MAC5E;MACAJ,OAAO,CAACK,YAAY,IAAIA,YAAY,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACL,EAAE,KAAKA,EAAE,CAAC,CAAC;MACpE;IAEJ,CAAC,QAAMM,KAAK,EAAC;MACTC,OAAO,CAACD,KAAK,CAACA,KAAK,CAACE,OAAO,CAAC;IAChC;EACJ,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAG;MACC,MAAMC,QAAQ,GAAG,MAAMT,KAAK,CAAC,gCAAgC,CAAC,EAAC;MAC/D,MAAMU,QAAQ,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;;MAEtC;MACAd,OAAO,CAACa,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAG;IAE9B,CAAC,QAAMP,KAAK,EAAC;MACTC,OAAO,CAACD,KAAK,CAACA,KAAK,CAACE,OAAO,CAAC;IAChC;EACJ,CAAC;EAEDjB,SAAS,CAAC,MAAM;IACdkB,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAC,EAAE,CAAC,EAAC;;EAEN,oBAEEf,OAAA,CAACL,QAAQ;IAAAyB,QAAA,GACN,GAAG,eAEJpB,OAAA;MAAAoB,QAAA,EAAI;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACrBxB,OAAA;MAAKyB,SAAS,EAAC,WAAW;MAAAL,QAAA,gBAC1BpB,OAAA;QAAAoB,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpBxB,OAAA;QAAAoB,QAAA,EAAG;MAA4F;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAClGxB,OAAA;QAAOyB,SAAS,EAAC,OAAO;QAAAL,QAAA,gBACvBpB,OAAA;UAAAoB,QAAA,eACEpB,OAAA;YAAAoB,QAAA,gBACEpB,OAAA;cAAAoB,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBxB,OAAA;cAAAoB,QAAA,EAAI;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACbxB,OAAA;cAAAoB,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAERxB,OAAA;UAAAoB,QAAA;UAKI;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;UACYjB,IAAI,CAACuB,GAAG,CAACf,IAAI,iBACXX,OAAA;YAAAoB,QAAA,gBACEpB,OAAA;cAAAoB,QAAA,EAAKT,IAAI,CAACgB;YAAW;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3BxB,OAAA;cAAAoB,QAAA,eAAIpB,OAAA,CAACF,IAAI;gBAACa,IAAI,EAAEA,IAAK;gBAACiB,QAAQ,EAAEb;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDxB,OAAA;cAAAoB,QAAA,eAAIpB,OAAA;gBAAQyB,SAAS,EAAC,gBAAgB;gBAACI,OAAO,EAAEA,CAAA,KAAMxB,UAAU,CAACM,IAAI,CAACL,EAAE,CAAE;gBAAAc,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAHxFb,IAAI,CAACL,EAAE,IAAIK,IAAI,CAACgB,WAAW;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIhC,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM,CAAC;AAIf,CAAC;AAACtB,EAAA,CA1FED,OAAO;AAAA6B,EAAA,GAAP7B,OAAO;AA4Fb,eAAeA,OAAO;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}