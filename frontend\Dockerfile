# --- Stage 1: Build the React App ---
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json (or yarn.lock)
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of your frontend application code
COPY . .

# Build the React app for production
RUN npm run build # This usually creates a 'build' or 'dist' folder

# --- Stage 2: Serve with Nginx ---
FROM nginx:stable-alpine

# Copy the static build files from the 'builder' stage to Nginx's web root
COPY --from=builder /app/build /usr/share/nginx/html

# (Optional) Copy a custom Nginx configuration if needed
# COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 80 (Nginx default)
EXPOSE 80

# Nginx will start automatically when the container runs
CMD ["nginx", "-g", "daemon off;"]
