{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Full Stack Practice\\\\frontend\\\\src\\\\pages\\\\Home.js\",\n  _s = $RefreshSig$();\n//styling\n//this demonstrates how to import css file at parent directory from css folder\nimport '../css/Home.css';\nimport { Fragment, useState } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Home = ({\n  item\n}) => {\n  _s();\n  // Default item to an empty object if it's undefined\n  const safeItem = item || {};\n  const [description, setDescription] = useState(safeItem.description || '');\n\n  //edit description \n  const updateDescription = async e => {\n    e.preventDefault();\n    if (!safeItem.id) {\n      console.error(\"No item ID available\");\n      return;\n    }\n    try {\n      const body = {\n        description\n      };\n      const response = await fetch(`http://localhost:5000/test-put/${safeItem.id}`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(body)\n      });\n      console.log(body);\n    } catch (error) {\n      console.error(error.message);\n    }\n  };\n\n  // Only render the edit button and modal if we have an item\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: safeItem.id ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        className: \"btn btn-primary btn-warning\",\n        \"data-toggle\": \"modal\",\n        \"data-target\": `#id${safeItem.id}`,\n        children: \"Edit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal\",\n        id: `id${safeItem.id}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-dialog\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"modal-title\",\n                children: \"Input Todo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"close\",\n                \"data-dismiss\": \"modal\",\n                onClick: e => updateDescription(e),\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-body\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"form-control\",\n                value: description,\n                onChange: e => setDescription(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-footer\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"btn btn-warning\",\n                \"data-dismiss\": \"modal\",\n                children: \"Edit\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"btn btn-danger\",\n                \"data-dismiss\": \"modal\",\n                children: \"Close\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Welcome to the Home page\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 11\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 7\n  }, this);\n};\n_s(Home, \"mTyzKkevmGS4hLtCcjzzm5JypiQ=\");\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["Fragment", "useState", "jsxDEV", "_jsxDEV", "_Fragment", "Home", "item", "_s", "safeItem", "description", "setDescription", "updateDescription", "e", "preventDefault", "id", "console", "error", "body", "response", "fetch", "method", "headers", "JSON", "stringify", "log", "message", "children", "type", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "value", "onChange", "target", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Full Stack Practice/frontend/src/pages/Home.js"], "sourcesContent": ["\r\n//styling\r\n//this demonstrates how to import css file at parent directory from css folder\r\nimport '../css/Home.css'; \r\nimport { Fragment, useState} from 'react';\r\n\r\n\r\nconst Home = ({ item }) => {\r\n    // Default item to an empty object if it's undefined\r\n    const safeItem = item || {};\r\n    const [description, setDescription] = useState(safeItem.description || '');\r\n\r\n    //edit description \r\n    const updateDescription = async (e) => {\r\n            e.preventDefault();\r\n            if (!safeItem.id) {\r\n                console.error(\"No item ID available\");\r\n                return;\r\n            }\r\n            try{\r\n                const body = { description }\r\n                const response = await fetch(`http://localhost:5000/test-put/${safeItem.id}`, {\r\n                  method: \"PUT\",\r\n                  headers: { \"Content-Type\": \"application/json\" },\r\n                  body: JSON.stringify(body)\r\n                });\r\n\r\n                console.log(body);\r\n\r\n            }catch(error){\r\n              console.error(error.message);\r\n            }\r\n    }\r\n\r\n    // Only render the edit button and modal if we have an item\r\n    return ( \r\n      <Fragment> \r\n        {safeItem.id ? (\r\n          <>\r\n            <button type=\"button\" className=\"btn btn-primary btn-warning\" data-toggle=\"modal\" data-target={`#id${safeItem.id}`}>\r\n              Edit\r\n            </button>\r\n\r\n            <div className=\"modal\" id={`id${safeItem.id}`}>\r\n              <div className=\"modal-dialog\">\r\n                <div className=\"modal-content\">\r\n\r\n\r\n                  <div className=\"modal-header\">\r\n                    <h4 className=\"modal-title\">Input Todo</h4>\r\n                    <button type=\"button\" className=\"close\" data-dismiss=\"modal\"\r\n                      onClick = { e => updateDescription(e)}\r\n                    \r\n                    >&times;</button>\r\n                  </div>\r\n\r\n               {/**\r\n                  *  value={description} set it first before onChange\r\n                 * onChange is used to update the text in the input \r\n                */}\r\n                  <div className=\"modal-body\">\r\n                    <input type=\"text\" className=\"form-control\" value={description} onChange={e => setDescription(e.target.value)} />\r\n                  </div>\r\n\r\n\r\n                  <div className=\"modal-footer\">\r\n                    <button type=\"button\" className=\"btn btn-warning\" data-dismiss=\"modal\">Edit</button>\r\n                    <button type=\"button\" className=\"btn btn-danger\" data-dismiss=\"modal\">Close</button>\r\n                  </div>\r\n\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </>\r\n        ) : (\r\n          <p>Welcome to the Home page</p>\r\n        )}\r\n      </Fragment>\r\n    )\r\n  };\r\n  \r\n  export default Home;\r\n"], "mappings": ";;AACA;AACA;AACA,OAAO,iBAAiB;AACxB,SAASA,QAAQ,EAAEC,QAAQ,QAAO,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAH,QAAA,IAAAI,SAAA;AAG1C,MAAMC,IAAI,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACvB;EACA,MAAMC,QAAQ,GAAGF,IAAI,IAAI,CAAC,CAAC;EAC3B,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAACO,QAAQ,CAACC,WAAW,IAAI,EAAE,CAAC;;EAE1E;EACA,MAAME,iBAAiB,GAAG,MAAOC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACL,QAAQ,CAACM,EAAE,EAAE;MACdC,OAAO,CAACC,KAAK,CAAC,sBAAsB,CAAC;MACrC;IACJ;IACA,IAAG;MACC,MAAMC,IAAI,GAAG;QAAER;MAAY,CAAC;MAC5B,MAAMS,QAAQ,GAAG,MAAMC,KAAK,CAAC,kCAAkCX,QAAQ,CAACM,EAAE,EAAE,EAAE;QAC5EM,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CJ,IAAI,EAAEK,IAAI,CAACC,SAAS,CAACN,IAAI;MAC3B,CAAC,CAAC;MAEFF,OAAO,CAACS,GAAG,CAACP,IAAI,CAAC;IAErB,CAAC,QAAMD,KAAK,EAAC;MACXD,OAAO,CAACC,KAAK,CAACA,KAAK,CAACS,OAAO,CAAC;IAC9B;EACR,CAAC;;EAED;EACA,oBACEtB,OAAA,CAACH,QAAQ;IAAA0B,QAAA,EACNlB,QAAQ,CAACM,EAAE,gBACVX,OAAA,CAAAC,SAAA;MAAAsB,QAAA,gBACEvB,OAAA;QAAQwB,IAAI,EAAC,QAAQ;QAACC,SAAS,EAAC,6BAA6B;QAAC,eAAY,OAAO;QAAC,eAAa,MAAMpB,QAAQ,CAACM,EAAE,EAAG;QAAAY,QAAA,EAAC;MAEpH;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET7B,OAAA;QAAKyB,SAAS,EAAC,OAAO;QAACd,EAAE,EAAE,KAAKN,QAAQ,CAACM,EAAE,EAAG;QAAAY,QAAA,eAC5CvB,OAAA;UAAKyB,SAAS,EAAC,cAAc;UAAAF,QAAA,eAC3BvB,OAAA;YAAKyB,SAAS,EAAC,eAAe;YAAAF,QAAA,gBAG5BvB,OAAA;cAAKyB,SAAS,EAAC,cAAc;cAAAF,QAAA,gBAC3BvB,OAAA;gBAAIyB,SAAS,EAAC,aAAa;gBAAAF,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3C7B,OAAA;gBAAQwB,IAAI,EAAC,QAAQ;gBAACC,SAAS,EAAC,OAAO;gBAAC,gBAAa,OAAO;gBAC1DK,OAAO,EAAKrB,CAAC,IAAID,iBAAiB,CAACC,CAAC,CAAE;gBAAAc,QAAA,EAEvC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eAMN7B,OAAA;cAAKyB,SAAS,EAAC,YAAY;cAAAF,QAAA,eACzBvB,OAAA;gBAAOwB,IAAI,EAAC,MAAM;gBAACC,SAAS,EAAC,cAAc;gBAACM,KAAK,EAAEzB,WAAY;gBAAC0B,QAAQ,EAAEvB,CAAC,IAAIF,cAAc,CAACE,CAAC,CAACwB,MAAM,CAACF,KAAK;cAAE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9G,CAAC,eAGN7B,OAAA;cAAKyB,SAAS,EAAC,cAAc;cAAAF,QAAA,gBAC3BvB,OAAA;gBAAQwB,IAAI,EAAC,QAAQ;gBAACC,SAAS,EAAC,iBAAiB;gBAAC,gBAAa,OAAO;gBAAAF,QAAA,EAAC;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpF7B,OAAA;gBAAQwB,IAAI,EAAC,QAAQ;gBAACC,SAAS,EAAC,gBAAgB;gBAAC,gBAAa,OAAO;gBAAAF,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACN,CAAC,gBAEH7B,OAAA;MAAAuB,QAAA,EAAG;IAAwB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG;EAC/B;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEf,CAAC;AAACzB,EAAA,CAxEEF,IAAI;AAAAgC,EAAA,GAAJhC,IAAI;AA0ER,eAAeA,IAAI;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}