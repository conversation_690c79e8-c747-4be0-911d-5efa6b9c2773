import React, {Fragment, useState, useEffect} from 'react';

//get edit component from Home.js
import Home from "./Home.js";


const Contact = () => {
    //todo is from the db table
    const [todo, setTodo] = useState([]);

    //delete todo
    
    const deleteTodo = async (id) => {
       // try{
       //     const deleteTodo = await fetch(`http://localhost:5000/test-delete/${id}`, {method: 'DELETE'}) //this is a get request
           
            //console.log(deleteTodo); test in the browser

       try {
            await fetch(`http://localhost:5000/test-delete/${id}`, { method: 'DELETE' });
            // Update the state to reflect the deletion
            setTodo(currentTodos => currentTodos.filter(item => item.id !== id)); 
            //console.log(deleteTodo); test in the browser

        }catch(error){
            console.error(error.message);
        }
    }

    const getToDos = async () => {
        try{
            const response = await fetch('http://localhost:5000/test-get') //this is a get request
            const jsonData = await response.json();

            //console.log(jsonData);      this will appear in the console
            setTodo(jsonData.data);   //this must be jsonData.data
            
        }catch(error){
            console.error(error.message);
        }
    }

    useEffect(() => {
      getToDos();
    },[]) //put [] to make sure this will return 1 array

    return (
      
      <Fragment>
        {" "}

        <h1>Contact Me</h1>
      <div className="container">
      <h2>Basic Table</h2>
      <p>The .table class adds basic styling (light padding and only horizontal dividers) to a table:</p>            
       <table className="table">
        <thead>
          <tr>
            <th>Description</th>
            <th>Edit</th>
            <th>Delete</th>
          </tr>
        </thead>

        <tbody>
 


           {
            /*
            Example of a static row (commented out):
            <tr>
              <td>John</td>
              <td>Doe</td>
              <td><EMAIL></td>
            </tr>
            */
            todo.map(item => (
              <tr key={item.id || item.description}>
                <td>{item.description}</td>
                <td><Home item={item} onUpdate={getToDos} /></td>
                <td><button className="btn btn-danger" onClick={() => deleteTodo(item.id)}>Delete</button></td>
              </tr>
            ))
          }




        </tbody>
      </table>
    </div>
      </Fragment>
      

    )
  };
  
export default Contact;
