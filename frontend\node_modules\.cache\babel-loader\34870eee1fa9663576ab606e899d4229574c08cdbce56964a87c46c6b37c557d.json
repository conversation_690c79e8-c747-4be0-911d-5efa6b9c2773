{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Full Stack Practice\\\\frontend\\\\src\\\\pages\\\\Home.js\",\n  _s = $RefreshSig$();\n//styling\n//this demonstrates how to import css file at parent directory from css folder\nimport '../css/Home.css';\nimport { Fragment, useState } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Home = ({\n  item,\n  onUpdate\n}) => {\n  _s();\n  // Default item to an empty object if it's undefined\n  const safeItem = item || {};\n  const [description, setDescription] = useState(safeItem.description || '');\n\n  //edit description\n  const updateDescription = async e => {\n    e.preventDefault();\n    if (!safeItem.id) {\n      console.error(\"No item ID available\");\n      return;\n    }\n    try {\n      const body = {\n        description\n      };\n      const response = await fetch(`http://localhost:5000/test-put/${safeItem.id}`, {\n        method: \"PUT\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(body)\n      });\n      if (response.ok) {\n        console.log(\"Update successful:\", body);\n\n        // Close the modal after successful update\n        const modal = document.getElementById(`id${safeItem.id}`);\n        if (modal) {\n          // Use Bootstrap's modal hide method if available, otherwise use jQuery\n          if (window.$ && window.$.fn.modal) {\n            window.$(`#id${safeItem.id}`).modal('hide');\n          } else {\n            modal.style.display = 'none';\n            modal.classList.remove('show');\n            document.body.classList.remove('modal-open');\n            // Remove backdrop if it exists\n            const backdrop = document.querySelector('.modal-backdrop');\n            if (backdrop) {\n              backdrop.remove();\n            }\n          }\n        }\n\n        // Call the callback function to notify parent component\n        if (onUpdate && typeof onUpdate === 'function') {\n          onUpdate();\n        }\n      } else {\n        console.error(\"Update failed with status:\", response.status);\n      }\n    } catch (error) {\n      console.error(\"Error updating description:\", error.message);\n    }\n  };\n\n  // Only render the edit button and modal if we have an item\n  return /*#__PURE__*/_jsxDEV(Fragment, {\n    children: safeItem.id ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        className: \"btn btn-primary btn-warning\",\n        \"data-toggle\": \"modal\",\n        \"data-target\": `#id${safeItem.id}`,\n        children: \"Edit\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal\",\n        id: `id${safeItem.id}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-dialog\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"modal-title\",\n                children: \"Edit Todo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"close\",\n                \"data-dismiss\": \"modal\",\n                children: \"\\xD7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-body\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                className: \"form-control\",\n                value: description,\n                onChange: e => setDescription(e.target.value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-footer\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"btn btn-success\",\n                onClick: updateDescription,\n                children: \"Update\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"btn btn-secondary\",\n                \"data-dismiss\": \"modal\",\n                onclick: () => se,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Welcome to the Home page\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 11\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 7\n  }, this);\n};\n_s(Home, \"mTyzKkevmGS4hLtCcjzzm5JypiQ=\");\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["Fragment", "useState", "jsxDEV", "_jsxDEV", "_Fragment", "Home", "item", "onUpdate", "_s", "safeItem", "description", "setDescription", "updateDescription", "e", "preventDefault", "id", "console", "error", "body", "response", "fetch", "method", "headers", "JSON", "stringify", "ok", "log", "modal", "document", "getElementById", "window", "$", "fn", "style", "display", "classList", "remove", "backdrop", "querySelector", "status", "message", "children", "type", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "target", "onClick", "onclick", "se", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Full Stack Practice/frontend/src/pages/Home.js"], "sourcesContent": ["\r\n//styling\r\n//this demonstrates how to import css file at parent directory from css folder\r\nimport '../css/Home.css'; \r\nimport { Fragment, useState} from 'react';\r\n\r\n\r\nconst Home = ({ item, onUpdate }) => {\r\n    // Default item to an empty object if it's undefined\r\n    const safeItem = item || {};\r\n    const [description, setDescription] = useState(safeItem.description || '');\r\n\r\n    //edit description\r\n    const updateDescription = async (e) => {\r\n            e.preventDefault();\r\n            if (!safeItem.id) {\r\n                console.error(\"No item ID available\");\r\n                return;\r\n            }\r\n            try{\r\n                const body = { description }\r\n                const response = await fetch(`http://localhost:5000/test-put/${safeItem.id}`, {\r\n                  method: \"PUT\",\r\n                  headers: { \"Content-Type\": \"application/json\" },\r\n                  body: JSON.stringify(body)\r\n                });\r\n\r\n                if (response.ok) {\r\n                    console.log(\"Update successful:\", body);\r\n\r\n                    // Close the modal after successful update\r\n                    const modal = document.getElementById(`id${safeItem.id}`);\r\n                    if (modal) {\r\n                        // Use Bootstrap's modal hide method if available, otherwise use jQuery\r\n                        if (window.$ && window.$.fn.modal) {\r\n                            window.$(`#id${safeItem.id}`).modal('hide');\r\n                        } else {\r\n                            modal.style.display = 'none';\r\n                            modal.classList.remove('show');\r\n                            document.body.classList.remove('modal-open');\r\n                            // Remove backdrop if it exists\r\n                            const backdrop = document.querySelector('.modal-backdrop');\r\n                            if (backdrop) {\r\n                                backdrop.remove();\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    // Call the callback function to notify parent component\r\n                    if (onUpdate && typeof onUpdate === 'function') {\r\n                        onUpdate();\r\n                    }\r\n                } else {\r\n                    console.error(\"Update failed with status:\", response.status);\r\n                }\r\n\r\n            }catch(error){\r\n              console.error(\"Error updating description:\", error.message);\r\n            }\r\n    }\r\n\r\n    // Only render the edit button and modal if we have an item\r\n    return ( \r\n      <Fragment> \r\n        {safeItem.id ? (\r\n          <>\r\n            <button type=\"button\" className=\"btn btn-primary btn-warning\" data-toggle=\"modal\" data-target={`#id${safeItem.id}`}>\r\n              Edit\r\n            </button>\r\n\r\n            <div className=\"modal\" id={`id${safeItem.id}`}>\r\n              <div className=\"modal-dialog\">\r\n                <div className=\"modal-content\">\r\n\r\n\r\n                  <div className=\"modal-header\">\r\n                    <h4 className=\"modal-title\">Edit Todo</h4>\r\n                    <button type=\"button\" className=\"close\" data-dismiss=\"modal\">&times;</button>\r\n                  </div>\r\n\r\n               {/**\r\n                  *  value={description} set it first before onChange\r\n                 * onChange is used to update the text in the input \r\n                */}\r\n                  <div className=\"modal-body\">\r\n                    <input type=\"text\" className=\"form-control\" value={description} onChange={e => setDescription(e.target.value)} />\r\n                  </div>\r\n\r\n\r\n                  <div className=\"modal-footer\">\r\n                    <button type=\"button\" className=\"btn btn-success\" onClick={updateDescription}>Update</button>\r\n                    <button type=\"button\" className=\"btn btn-secondary\" data-dismiss=\"modal\" onclick={ () => se}>Cancel</button>\r\n                  </div>\r\n\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </>\r\n        ) : (\r\n          <p>Welcome to the Home page</p>\r\n        )}\r\n      </Fragment>\r\n    )\r\n  };\r\n  \r\n  export default Home;\r\n"], "mappings": ";;AACA;AACA;AACA,OAAO,iBAAiB;AACxB,SAASA,QAAQ,EAAEC,QAAQ,QAAO,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAH,QAAA,IAAAI,SAAA;AAG1C,MAAMC,IAAI,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACjC;EACA,MAAMC,QAAQ,GAAGH,IAAI,IAAI,CAAC,CAAC;EAC3B,MAAM,CAACI,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAACQ,QAAQ,CAACC,WAAW,IAAI,EAAE,CAAC;;EAE1E;EACA,MAAME,iBAAiB,GAAG,MAAOC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACL,QAAQ,CAACM,EAAE,EAAE;MACdC,OAAO,CAACC,KAAK,CAAC,sBAAsB,CAAC;MACrC;IACJ;IACA,IAAG;MACC,MAAMC,IAAI,GAAG;QAAER;MAAY,CAAC;MAC5B,MAAMS,QAAQ,GAAG,MAAMC,KAAK,CAAC,kCAAkCX,QAAQ,CAACM,EAAE,EAAE,EAAE;QAC5EM,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CJ,IAAI,EAAEK,IAAI,CAACC,SAAS,CAACN,IAAI;MAC3B,CAAC,CAAC;MAEF,IAAIC,QAAQ,CAACM,EAAE,EAAE;QACbT,OAAO,CAACU,GAAG,CAAC,oBAAoB,EAAER,IAAI,CAAC;;QAEvC;QACA,MAAMS,KAAK,GAAGC,QAAQ,CAACC,cAAc,CAAC,KAAKpB,QAAQ,CAACM,EAAE,EAAE,CAAC;QACzD,IAAIY,KAAK,EAAE;UACP;UACA,IAAIG,MAAM,CAACC,CAAC,IAAID,MAAM,CAACC,CAAC,CAACC,EAAE,CAACL,KAAK,EAAE;YAC/BG,MAAM,CAACC,CAAC,CAAC,MAAMtB,QAAQ,CAACM,EAAE,EAAE,CAAC,CAACY,KAAK,CAAC,MAAM,CAAC;UAC/C,CAAC,MAAM;YACHA,KAAK,CAACM,KAAK,CAACC,OAAO,GAAG,MAAM;YAC5BP,KAAK,CAACQ,SAAS,CAACC,MAAM,CAAC,MAAM,CAAC;YAC9BR,QAAQ,CAACV,IAAI,CAACiB,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;YAC5C;YACA,MAAMC,QAAQ,GAAGT,QAAQ,CAACU,aAAa,CAAC,iBAAiB,CAAC;YAC1D,IAAID,QAAQ,EAAE;cACVA,QAAQ,CAACD,MAAM,CAAC,CAAC;YACrB;UACJ;QACJ;;QAEA;QACA,IAAI7B,QAAQ,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;UAC5CA,QAAQ,CAAC,CAAC;QACd;MACJ,CAAC,MAAM;QACHS,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEE,QAAQ,CAACoB,MAAM,CAAC;MAChE;IAEJ,CAAC,QAAMtB,KAAK,EAAC;MACXD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAACuB,OAAO,CAAC;IAC7D;EACR,CAAC;;EAED;EACA,oBACErC,OAAA,CAACH,QAAQ;IAAAyC,QAAA,EACNhC,QAAQ,CAACM,EAAE,gBACVZ,OAAA,CAAAC,SAAA;MAAAqC,QAAA,gBACEtC,OAAA;QAAQuC,IAAI,EAAC,QAAQ;QAACC,SAAS,EAAC,6BAA6B;QAAC,eAAY,OAAO;QAAC,eAAa,MAAMlC,QAAQ,CAACM,EAAE,EAAG;QAAA0B,QAAA,EAAC;MAEpH;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAET5C,OAAA;QAAKwC,SAAS,EAAC,OAAO;QAAC5B,EAAE,EAAE,KAAKN,QAAQ,CAACM,EAAE,EAAG;QAAA0B,QAAA,eAC5CtC,OAAA;UAAKwC,SAAS,EAAC,cAAc;UAAAF,QAAA,eAC3BtC,OAAA;YAAKwC,SAAS,EAAC,eAAe;YAAAF,QAAA,gBAG5BtC,OAAA;cAAKwC,SAAS,EAAC,cAAc;cAAAF,QAAA,gBAC3BtC,OAAA;gBAAIwC,SAAS,EAAC,aAAa;gBAAAF,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1C5C,OAAA;gBAAQuC,IAAI,EAAC,QAAQ;gBAACC,SAAS,EAAC,OAAO;gBAAC,gBAAa,OAAO;gBAAAF,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eAMN5C,OAAA;cAAKwC,SAAS,EAAC,YAAY;cAAAF,QAAA,eACzBtC,OAAA;gBAAOuC,IAAI,EAAC,MAAM;gBAACC,SAAS,EAAC,cAAc;gBAACK,KAAK,EAAEtC,WAAY;gBAACuC,QAAQ,EAAEpC,CAAC,IAAIF,cAAc,CAACE,CAAC,CAACqC,MAAM,CAACF,KAAK;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9G,CAAC,eAGN5C,OAAA;cAAKwC,SAAS,EAAC,cAAc;cAAAF,QAAA,gBAC3BtC,OAAA;gBAAQuC,IAAI,EAAC,QAAQ;gBAACC,SAAS,EAAC,iBAAiB;gBAACQ,OAAO,EAAEvC,iBAAkB;gBAAA6B,QAAA,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7F5C,OAAA;gBAAQuC,IAAI,EAAC,QAAQ;gBAACC,SAAS,EAAC,mBAAmB;gBAAC,gBAAa,OAAO;gBAACS,OAAO,EAAGA,CAAA,KAAMC,EAAG;gBAAAZ,QAAA,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACN,CAAC,gBAEH5C,OAAA;MAAAsC,QAAA,EAAG;IAAwB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG;EAC/B;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEf,CAAC;AAACvC,EAAA,CAhGEH,IAAI;AAAAiD,EAAA,GAAJjD,IAAI;AAkGR,eAAeA,IAAI;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}